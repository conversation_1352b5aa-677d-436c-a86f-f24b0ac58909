package main

import (
	"log"
	"pinkdesign/api/src/pkg/loader"
	"pinkdesign/api/src/pkg/logger"

	"github.com/danielgtaylor/huma/v2"
	"github.com/go-chi/chi/v5"
	_ "github.com/jackc/pgx/v5/stdlib"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/viper"
)

type Dependency string

const (
	HttpServer Dependency = "server"
	Config     Dependency = "config"
	Database   Dependency = "database"
)

var ConsulKeys = []string{
	"app/configs.toml",
	"database/postgres.toml",
}

type App struct {
	Server   *chi.Mux
	Api      huma.API
	Database *sqlx.DB
}

func Bootstrap(dependencies []Dependency) *App {
	app := &App{}

	for _, dependency := range dependencies {
		switch dependency {
		case Config:
			consulAddr := loader.GetConsulAddr()
			err := loader.LoadConfigFromConsul(<PERSON><PERSON><PERSON><PERSON>, consulAddr)
			if err != nil {
				logger.Error(err).Msg("can not load config from consul")
			}
		case Database:
			db, err := sqlx.Connect("pgx", viper.GetString("postgres.database_url"))
			if err != nil {
				logger.Error(err).Msg("Can not connect to database!")
				log.Fatal("Can not connect to database!")
			}
			app.Database = db

		case HttpServer:
			bootstrapHttpServer(app)
		}
	}

	return app
}
