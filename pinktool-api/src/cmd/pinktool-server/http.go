package main

import (
	"context"
	"fmt"
	"net/http"
	apisV1 "pinkdesign/api/src/internal/apis/v1"
	accountService "pinkdesign/api/src/internal/services/account_service"
	adminService "pinkdesign/api/src/internal/services/admin_service"
	"pinkdesign/api/src/pkg/logger"
	"strings"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humachi"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	"github.com/segmentio/ksuid"
)

func bootstrapHttpServer(app *App) {
	app.Server = chi.NewMux()

	app.Server.Use(func(next http.Handler) http.Handler {
		fn := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := context.WithValue(r.Context(), logger.RequestIdKey, ksuid.New().String())
			log := logger.Info().Ctx(ctx)
			queryString := ""
			for key, val := range r.URL.Query() {
				if key != "token" {
					queryString += (key + "=" + strings.Join(val, ",") + ";")
				}
			}
			if queryString != "" {
				log = log.Str("query", queryString)
			}

			log.Msg(fmt.Sprintf("%s %s", r.Method, r.URL.Path))
			next.ServeHTTP(w, r.WithContext(ctx))
		})
		return fn
	})

	config := huma.DefaultConfig("zodibase API", "1.0.0")

	config.CreateHooks = []func(huma.Config) huma.Config{
		func(c huma.Config) huma.Config {
			return c
		},
	}

	app.Server.Use(cors.Handler(cors.Options{
		// AllowedOrigins:   []string{"https://foo.com"}, // Use this to allow specific origin hosts
		AllowedOrigins: []string{"http://localhost:5173*", "https://localhost:5173*"},
		// AllowOriginFunc:  func(r *http.Request, origin string) bool { return true },
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token", "Workspace"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: false,
		MaxAge:           300, // Maximum value not ignored by any of major browsers
	}))

	adapter := humachi.NewAdapter(app.Server)
	rootapi := huma.NewAPI(config, adapter)

	rootapi.UseMiddleware(func(ctx huma.Context, next func(c huma.Context)) {
		fmt.Printf("[%s] %s", ctx.Method(), ctx.URL().Path)
		next(ctx)
	})

	accService := accountService.NewService(app.Database)
	adService := adminService.NewService(app.Database)
	middleware := apisV1.NewMiddleware(accService)

	app.Server.Group(func(root chi.Router) {
		adt := humachi.NewAdapter(root)
		api := huma.NewAPI(config, adt)

		// auth
		authHandler := apisV1.NewAuthHandler(app.Database)
		authHandler.RegisterRoutes(api)

		// webhook
		healthcheckHandler := apisV1.NewHealthcheckHandler()

		healthcheckHandler.RegisterRoutes(api)
	})

	app.Server.Group(func(root chi.Router) {
		adt := humachi.NewAdapter(root)
		api := huma.NewAPI(config, adt)

		api.UseMiddleware(middleware.WithAuth(api))

		// account
		accountHandler := apisV1.NewAccountHandler(accService)
		accountHandler.RegisterRoutes(api)
	})

	app.Server.Group(func(root chi.Router) {
		adt := humachi.NewAdapter(root)
		api := huma.NewAPI(config, adt)

		api.UseMiddleware(middleware.WithAuth(api))
		api.UseMiddleware(middleware.WithRole(api, []string{"admin"}))

		// admin
		adminHandler := apisV1.NewAdminHandler(adService)
		adminHandler.RegisterRoutes(api)
	})
}
