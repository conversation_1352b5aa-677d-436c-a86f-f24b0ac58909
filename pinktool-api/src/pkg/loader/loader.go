package loader

import (
	"bytes"
	"fmt"
	"os"
	"pinkdesign/api/src/pkg/logger"

	"github.com/hashicorp/consul/api"
	"github.com/spf13/viper"
)

func GetConsulAddr() string {
	consulAddr := os.Getenv("CONSUL_ADDR")
	if consulAddr != "" {
		return consulAddr
	}
	return "localhost:8500"
}

func LoadConfigFromConsul(keys []string, addr string) error {
	config := api.DefaultConfig()
	config.Address = addr
	config.Token = os.Getenv("CONSUL_TOKEN")

	client, err := api.NewClient(config)
	if err != nil {
		return err
	}

	kv := client.KV()
	viper.SetConfigType("toml")
	for _, key := range keys {
		pair, _, err := kv.Get(key, nil)
		if err != nil {
			return fmt.Errorf("%v %v", key, err.<PERSON>rror())
		}

		if pair != nil {
			if err := viper.MergeConfig(bytes.NewReader(pair.Value)); err != nil {
				return fmt.Errorf("%v %v", key, err.Error())
			}
		}
		logger.Info().Msgf("loaded key %v", key)
	}

	return nil
}
