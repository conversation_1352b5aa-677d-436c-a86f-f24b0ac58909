package permissions

type Permission int64

const (
	Read Permission = 1 << iota
)

func (p Permission) Has(permission Permission) bool {
	return p&permission == permission
}

func (p Permission) Add(permission Permission) Permission {
	return p | permission
}

func (p Permission) Remove(permission Permission) Permission {
	return p &^ permission
}

func NewGroup(permissions ...Permission) Permission {
	var group Permission
	for _, permission := range permissions {
		group = group.Add(permission)
	}
	return group
}
