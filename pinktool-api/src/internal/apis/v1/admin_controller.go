package apisV1

import (
	"context"
	accountModel "pinkdesign/api/src/internal/models/account"

	"github.com/danielgtaylor/huma/v2"
)

type handleGetClientsInput struct {
	Limit uint `json:"limit"`
	Page  uint `json:"page"`
}
type handleGetClientsResponse struct {
	Body struct {
		TotalEntries int64                  `json:"total_entries"`
		CurrentPage  int64                  `json:"current_page"`
		Entries      []accountModel.Account `json:"entries"`
	}
}

func (h *AdminHandler) handleGetClients(ctx context.Context, input *handleGetClientsInput) (*handleGetClientsResponse, error) {
	page := max(input.Page, 1)
	limit := max(input.Limit, 10)
	clients, total, err := h.adminService.GetClients(limit, page)
	if err != nil {
		return nil, huma.Error500InternalServerError("Can not get clients", err)
	}

	ret := &handleGetClientsResponse{}
	ret.Body.TotalEntries = int64(total)
	ret.Body.CurrentPage = int64(page)
	ret.Body.Entries = clients
	return ret, nil
}

type handleUpdateClientInput struct {
	ClientID string `path:"client_id"`
	Body     struct {
		Status string `json:"status"`
	}
}

type handleUpdateClientResponse struct {
	Body struct {
		Message string `json:"message"`
	}
}

func (h *AdminHandler) handleUpdateClient(ctx context.Context, input *handleUpdateClientInput) (*handleUpdateClientResponse, error) {
	err := h.adminService.UpdateClientStatus(input.ClientID, input.Body.Status)
	if err != nil {
		return nil, huma.Error500InternalServerError("Can not update client status", err)
	}

	ret := &handleUpdateClientResponse{}
	ret.Body.Message = "Client status updated successfully"
	return ret, nil
}
