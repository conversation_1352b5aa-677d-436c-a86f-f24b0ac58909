package apisV1

import (
	"fmt"
	"net/http"
	accountService "pinkdesign/api/src/internal/services/account_service"
	"slices"
	"strings"

	"github.com/danielgtaylor/huma/v2"
	"github.com/o1egl/paseto"
	"github.com/spf13/viper"
)

type EmptyInput struct{}
type EmptyResponse struct{}

type Middleware struct {
	AccountService *accountService.Service
}

func NewMiddleware(accService *accountService.Service) *Middleware {
	return &Middleware{
		AccountService: accService,
	}
}

func (m *Middleware) WithAuth(api huma.API) func(context huma.Context, next func(context huma.Context)) {
	return func(context huma.Context, next func(context huma.Context)) {
		authorization := strings.Split(context.Header("Authorization"), " ")
		if len(authorization) != 2 {
			_ = huma.WriteErr(api, context, http.StatusBadRequest,
				"Authorization is invalid", fmt.Errorf("token or prefix is invalid"),
			)
			return
		}
		if authorization[0] != "Bearer" {
			_ = huma.WriteErr(api, context, http.StatusUnauthorized,
				"Authorization prefix missing", fmt.Errorf("bearer prefix is required"),
			)
			return
		}

		var payload paseto.JSONToken
		var token = authorization[1]
		var footer string
		err := paseto.NewV2().Decrypt(token, []byte(viper.GetString("apps.secret")), &payload, &footer)
		if err != nil {
			_ = huma.WriteErr(api, context, http.StatusUnauthorized,
				"Token is invalid or expired", fmt.Errorf("token is invalid or expired"),
			)
			return
		}
		account_id := payload.Get("account_id")
		if err != nil {
			_ = huma.WriteErr(api, context, http.StatusBadRequest,
				"Can not parse account_id", fmt.Errorf("can not parse account_id"),
			)
			return
		}
		context = huma.WithValue(context, "account_id", account_id)
		next(context)
	}
}

func (m *Middleware) WithRole(api huma.API, roleName []string) func(context huma.Context, next func(context huma.Context)) {
	return func(ctx huma.Context, next func(context huma.Context)) {
		account_id := ctx.Context().Value("account_id").(string)
		check, err := m.AccountService.GetAccountRole(account_id)
		if err != nil {
			fmt.Printf("err: %v", err)
			_ = huma.WriteErr(api, ctx, http.StatusInternalServerError,
				"Can not get account role", fmt.Errorf("can not get account role"),
			)
			return
		}
		if slices.Contains(roleName, check.RoleName) {
			next(ctx)
		} else {
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized,
				"Unauthorized", fmt.Errorf("unauthorized"),
			)
		}
	}
}
