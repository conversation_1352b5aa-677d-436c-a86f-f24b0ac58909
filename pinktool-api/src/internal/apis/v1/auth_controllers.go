package apisV1

import (
	"context"
	"fmt"
	"net/http"
	"pinkdesign/api/src/pkg/logger"

	"github.com/danielgtaylor/huma/v2"
)

type (
	LoginInput struct {
		Body struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}
	}
	LoginOutput struct {
		Body struct {
			Token string `json:"token"`
		}
	}
	SignupInput struct {
		Body struct {
			FullName        string `json:"full_name"`
			Email           string `json:"email"`
			Password        string `json:"password"`
			ConfirmPassword string `json:"confirm_password"`
		}
	}
	SignupOutput struct {
		Body struct {
			Id        string `json:"id"`
			FullName  string `json:"full_name"`
			Email     string `json:"email"`
			CreatedAt string `json:"created_at"`
			UpdatedAt string `json:"updated_at"`
		}
	}
)

func (h *AuthHandler) handleLogin(ctx context.Context, input *LoginInput) (*LoginOutput, error) {
	ret := &LoginOutput{}
	account, err := h.accountService.GetAccountByEmail(input.Body.Email)
	if err != nil {
		logger.Error(err).Ctx(ctx).Msg("GetAccountByEmail failed!")
		nerr := huma.NewError(http.StatusUnauthorized,
			"Unauthorized", fmt.Errorf("Account associate with this email does not exist"),
		)
		return ret, nerr
	}

	token, err := h.authService.GetAccountToken(input.Body.Password, account)
	if err != nil {
		logger.Error(err).Ctx(ctx).Msg("GetAccountToken failed!")
		return nil, err
	}

	ret.Body.Token = token

	return ret, nil
}

func (h *AuthHandler) handleSignup(ctx context.Context, input *SignupInput) (*SignupOutput, error) {
	ret := &SignupOutput{}
	if input.Body.Password != input.Body.ConfirmPassword {
		err := huma.NewError(http.StatusBadRequest, "Bad request", fmt.Errorf("Password and Confirm Password does not match"))
		return ret, err
	}
	account, err := h.authService.CreateAccount(input.Body.Email, input.Body.FullName, input.Body.Password)
	if err != nil {
		logger.Error(err).Ctx(ctx).Msg("CreateAccount failed!")
		return nil, err
	}
	ret.Body.Id = account.Id
	ret.Body.FullName = account.FullName
	ret.Body.Email = account.Email
	ret.Body.CreatedAt = account.CreatedAt
	ret.Body.UpdatedAt = account.UpdatedAt

	return ret, nil
}
