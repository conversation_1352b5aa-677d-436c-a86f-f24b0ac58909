package apisV1

import (
	"net/http"
	accountService "pinkdesign/api/src/internal/services/account_service"
	authService "pinkdesign/api/src/internal/services/auth_service"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jmoiron/sqlx"
)

type AuthHandler struct {
	db             *sqlx.DB
	accountService *accountService.Service
	authService    *authService.Service
}

func NewAuthHandler(db *sqlx.DB) *AuthHandler {
	service := authService.NewService(db)
	accountService := accountService.NewService(db)

	return &AuthHandler{
		db:             db,
		authService:    service,
		accountService: accountService,
	}
}

func (h *AuthHandler) RegisterRoutes(api huma.API) {
	tags := []string{"Auth"}

	prefix := "/auth"

	huma.Register(api, huma.Operation{
		OperationID: "login",
		Method:      http.MethodPost,
		Path:        prefix + "/login",
		Tags:        tags,
	}, h.handleLogin)

	huma.Register(api, huma.Operation{
		OperationID: "signup",
		Method:      http.MethodPost,
		Path:        prefix + "/signup",
		Tags:        tags,
	}, h.handleSignup)
}
