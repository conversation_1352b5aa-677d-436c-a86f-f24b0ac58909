package apisV1

import (
	"context"

	"github.com/danielgtaylor/huma/v2"
)

type GetMeResponse struct {
	Body struct {
		Id         string `json:"id"`
		FullName   string `json:"full_name"`
		ProfilePic string `json:"profile_picture"`
		Email      string `json:"email"`
		CreatedAt  string `json:"created_at"`
		UpdatedAt  string `json:"updated_at"`
		Status     string `json:"status"`
		RoleId     int    `json:"role_id"`
		RoleName   string `json:"role_name"`
	}
}

func (h *AccountHandler) handleGetMe(ctx context.Context, input *EmptyInput) (*GetMeResponse, error) {
	ret := &GetMeResponse{}
	accountId := ctx.Value("account_id").(string)
	account, err := h.accountService.GetAccountById(accountId)
	if err != nil {
		return nil, huma.Error404NotFound("Account not found", err)
	}
	role, err := h.accountService.GetRoleById(account.RoleID)
	if err != nil {
		return nil, huma.Error500InternalServerError("Can not get role", err)
	}
	ret.Body.Id = account.ID
	ret.Body.FullName = account.FullName
	ret.Body.Email = account.Email
	ret.Body.CreatedAt = account.CreatedAt
	ret.Body.UpdatedAt = account.UpdatedAt
	ret.Body.ProfilePic = account.ProfilePic
	ret.Body.Status = account.Status
	ret.Body.RoleId = role.ID
	ret.Body.RoleName = role.Name
	return ret, nil
}
