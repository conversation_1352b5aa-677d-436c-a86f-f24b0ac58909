package apisV1

import (
	"net/http"
	adminService "pinkdesign/api/src/internal/services/admin_service"

	"github.com/danielgtaylor/huma/v2"
)

type AdminHandler struct {
	adminService *adminService.Service
}

func NewAdminHandler(adminService *adminService.Service) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
	}
}

func (h *AdminHandler) RegisterRoutes(api huma.API) {
	tags := []string{"Admin"}

	prefix := "/admin"

	huma.Register(api, huma.Operation{
		OperationID: "get_clients",
		Method:      http.MethodGet,
		Path:        prefix + "/clients",
		Tags:        tags,
	}, h.handleGetClients)
}
