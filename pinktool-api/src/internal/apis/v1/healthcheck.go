package apisV1

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
)

type HealthcheckHandler struct{}

func NewHealthcheckHandler() *HealthcheckHandler {
	return &HealthcheckHandler{}
}

func (h *HealthcheckHandler) RegisterRoutes(api huma.API) {
	tags := []string{"Healthcheck"}

	huma.Register(api, huma.Operation{
		OperationID: "ping",
		Method:      http.MethodGet,
		Path:        "/healthcheck/ping",
		Tags:        tags,
	}, h.ping)
}

type pingOutput struct {
	Body struct {
		Message string `json:"message" example:"pong"`
	}
}

func (h *HealthcheckHandler) ping(ctx context.Context, input *EmptyInput) (*pingOutput, error) {
	resp := &pingOutput{}
	resp.Body.Message = "pong!"
	return resp, nil
}
