package apisV1

import (
	"net/http"
	accountService "pinkdesign/api/src/internal/services/account_service"

	"github.com/danielgtaylor/huma/v2"
)

type AccountHandler struct {
	accountService *accountService.Service
}

func NewAccountHandler(accountService *accountService.Service) *AccountHandler {
	return &AccountHandler{
		accountService: accountService,
	}
}

func (h *AccountHandler) RegisterRoutes(api huma.API) {
	tags := []string{"Account"}

	prefix := "/account"

	huma.Register(api, huma.Operation{
		OperationID: "get_me",
		Method:      http.MethodGet,
		Path:        prefix + "/me",
		Tags:        tags,
	}, h.handleGetMe)
}
