package adminService

import (
	"fmt"
	accountModel "pinkdesign/api/src/internal/models/account"

	"github.com/doug-martin/goqu/v9"
	"github.com/jmoiron/sqlx"
)

type Service struct {
	database *sqlx.DB
}

func NewService(database *sqlx.DB) *Service {
	return &Service{
		database: database,
	}
}

func (s *Service) GetClients(limit, page uint) ([]accountModel.Account, int, error) {
	offset := max(page-1, 0) * limit

	query := goqu.From(goqu.T("accounts").As("a")).
		InnerJoin(goqu.T("roles").As("r"), goqu.On(goqu.I("a.role_id").Eq(goqu.I("r.id")))).
		Where(goqu.I("r.name").Eq("client"))

	totalQuery, _, _ := query.
		Select(goqu.L("COUNT(*)")).
		ToSQL()

	var total int
	err := s.database.Get(&total, totalQuery)
	if err != nil {
		return nil, 0, err
	}
	fmt.Printf("total: %v", total)

	q, _, _ := query.
		Select("a.*").
		Limit(limit).
		Offset(offset).
		ToSQL()

	var accounts []accountModel.Account
	err = s.database.Select(&accounts, q)
	if err != nil {
		return accounts, total, err
	}

	return accounts, total, nil
}

func (s *Service) UpdateClientStatus(clientId string, status string) error {
	q, args, _ := goqu.Update("accounts").
		Set(goqu.Record{"status": status}).
		Where(goqu.C("id").Eq(clientId)).
		ToSQL()

	_, err := s.database.Exec(q, args...)
	if err != nil {
		return err
	}

	return nil
}
