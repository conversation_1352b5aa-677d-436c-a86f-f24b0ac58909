package accountService

import (
	accountModel "pinkdesign/api/src/internal/models/account"

	"github.com/doug-martin/goqu/v9"
	"github.com/jmoiron/sqlx"
)

type Service struct {
	database *sqlx.DB
}

func NewService(db *sqlx.DB) *Service {
	return &Service{
		database: db,
	}
}

func (s *Service) GetAccountByEmail(email string) (accountModel.Account, error) {
	q, _, _ := goqu.From("accounts").
		Select("accounts.*").
		Where(goqu.C("email").Eq(email)).
		ToSQL()

	var account accountModel.Account
	err := s.database.Get(&account, q)
	if err != nil {
		return account, err
	}

	return account, nil
}

func (s *Service) GetRoleById(id int) (accountModel.Role, error) {
	q, _, _ := goqu.From("roles").
		Select("roles.*").
		Where(goqu.C("id").Eq(id)).
		ToSQL()

	var role accountModel.Role
	err := s.database.Get(&role, q)
	if err != nil {
		return role, err
	}

	return role, nil
}

func (s *Service) GetAccountById(id string) (accountModel.Account, error) {
	q, _, _ := goqu.From("accounts").
		Select("accounts.*").
		Where(goqu.C("id").Eq(id)).
		ToSQL()

	var account accountModel.Account
	err := s.database.Get(&account, q)
	if err != nil {
		return account, err
	}

	return account, nil
}

func (s *Service) GetAccountRole(accountId string) (accountModel.AccountRole, error) {
	q, _, _ := goqu.From(goqu.T("accounts").As("a")).
		Join(goqu.T("roles").As("r"), goqu.On(goqu.Ex{
			"a.role_id": goqu.I("r.id"),
		})).
		Select(
			goqu.I("a.id").As("account_id"),
			goqu.I("r.id").As("role_id"),
			goqu.I("r.name").As("role_name"),
			goqu.I("r.permissions"),
		).
		Where(goqu.I("a.id").Eq(accountId)).
		ToSQL()

	var accountRole accountModel.AccountRole
	err := s.database.Get(&accountRole, q)
	if err != nil {
		return accountRole, err
	}
	return accountRole, nil
}
