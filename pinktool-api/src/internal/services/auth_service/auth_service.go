package authService

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	accountModel "pinkdesign/api/src/internal/models/account"
	"strings"
	"time"

	"github.com/doug-martin/goqu/v9"
	"github.com/jmoiron/sqlx"
	"github.com/o1egl/paseto"
	"github.com/spf13/viper"
	"golang.org/x/crypto/argon2"
)

type Service struct {
	database *sqlx.DB
}

func NewService(db *sqlx.DB) *Service {
	return &Service{
		database: db,
	}
}

type argon2Params struct {
	memory      uint32
	iterations  uint32
	parallelism uint8
	saltLength  uint32
	keyLength   uint32
}

var (
	ErrInvalidHash         = errors.New("the encoded hash is not in the correct format")
	ErrIncompatibleVersion = errors.New("incompatible version of argon2")
)

type AccountResponse struct {
	Id        string `json:"id" db:"id"`
	Email     string `json:"email" db:"email"`
	FullName  string `json:"full_name" db:"full_name"`
	CreatedAt string `json:"created_at" db:"created_at"`
	UpdatedAt string `json:"updated_at" db:"updated_at"`
}

func (s *Service) CreateAccount(email, fullname, password string) (AccountResponse, error) {
	passwordHash, nerr := generatePasswordHash(password)
	if nerr != nil {
		return AccountResponse{}, nerr
	}

	existed, args, _ := goqu.From("accounts").
		Select("accounts.*").
		Where(goqu.Ex{"email": email}).
		ToSQL()

	var eaccount accountModel.Account
	// nolint:all
	s.database.Get(&eaccount, existed, args...)
	if eaccount.ID != "" {
		return AccountResponse{}, errors.New("account already exists")
	}

	q, args, _ := goqu.Insert("accounts").
		Rows(goqu.Record{
			"email":           strings.ToLower(strings.TrimSpace(email)),
			"full_name":       fullname,
			"password_hash":   passwordHash,
			"google_handle":   "",
			"facebook_handle": "",
			"role_id":         1, // Default user role
		}).
		Returning("id", "email", "full_name", "created_at", "updated_at").
		ToSQL()

	var account AccountResponse
	err := s.database.Get(&account, q, args...)
	if err != nil {
		return AccountResponse{}, err
	}

	return account, nil
}

func (s *Service) GetAccountToken(password string, account accountModel.Account) (string, error) {
	match, err := comparePasswordAndHash(password, account.PasswordHash)
	if err != nil {
		return "", err
	}

	if match {
		now := time.Now()
		exp := now.Add(24 * time.Hour)
		nbt := now
		jsonToken := paseto.JSONToken{
			IssuedAt:   now,
			Expiration: exp,
			NotBefore:  nbt,
		}
		jsonToken.Set("account_id", account.ID)
		footer := "marvy"

		secret := viper.GetString("apps.secret")
		token, err := paseto.NewV2().Encrypt([]byte(secret), jsonToken, footer)
		return token, err
	}

	return "", err
}

func generatePasswordHash(password string) (string, error) {
	p := &argon2Params{
		memory:      64 * 1024,
		iterations:  3,
		parallelism: 2,
		saltLength:  16,
		keyLength:   32,
	}

	salt, err := generateRandomBytes(p.saltLength)
	if err != nil {
		return "", err
	}

	hash := argon2.IDKey([]byte(password), salt, p.iterations, p.memory, p.parallelism, p.keyLength)

	b64Salt := base64.RawStdEncoding.EncodeToString(salt)
	b64Hash := base64.RawStdEncoding.EncodeToString(hash)

	encodedHash := fmt.Sprintf("$argon2id$v=%d$m=%d,t=%d,p=%d$%s$%s", argon2.Version, p.memory, p.iterations, p.parallelism, b64Salt, b64Hash)

	return encodedHash, nil
}

func comparePasswordAndHash(password, encodedHash string) (match bool, err error) {
	p, salt, hash, err := decodeHash(encodedHash)
	if err != nil {
		return false, err
	}

	otherHash := argon2.IDKey([]byte(password), salt, p.iterations, p.memory, p.parallelism, p.keyLength)

	if subtle.ConstantTimeCompare(hash, otherHash) == 1 {
		return true, nil
	}
	return false, nil
}

func decodeHash(encodedHash string) (p *argon2Params, salt, hash []byte, err error) {
	vals := strings.Split(encodedHash, "$")
	if len(vals) != 6 {
		return nil, nil, nil, ErrInvalidHash
	}

	var version int
	_, err = fmt.Sscanf(vals[2], "v=%d", &version)
	if err != nil {
		return nil, nil, nil, err
	}
	if version != argon2.Version {
		return nil, nil, nil, ErrIncompatibleVersion
	}

	p = &argon2Params{}
	_, err = fmt.Sscanf(vals[3], "m=%d,t=%d,p=%d", &p.memory, &p.iterations, &p.parallelism)
	if err != nil {
		return nil, nil, nil, err
	}

	salt, err = base64.RawStdEncoding.Strict().DecodeString(vals[4])
	if err != nil {
		return nil, nil, nil, err
	}
	p.saltLength = uint32(len(salt))

	hash, err = base64.RawStdEncoding.Strict().DecodeString(vals[5])
	if err != nil {
		return nil, nil, nil, err
	}
	p.keyLength = uint32(len(hash))

	return p, salt, hash, nil
}

func generateRandomBytes(n uint32) ([]byte, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return nil, err
	}

	return b, nil
}
