package accountModel

type Role struct {
	ID          int    `db:"id" json:"id"`
	Name        string `db:"name" json:"name"`
	Permissions int64  `db:"permissions" json:"permissions"`
	CreatedAt   string `db:"created_at" json:"created_at"`
	UpdatedAt   string `db:"updated_at" json:"updated_at"`
}

type Account struct {
	ID             string `db:"id" json:"id"`
	Email          string `db:"email" json:"email"`
	FullName       string `db:"full_name" json:"full_name"`
	ProfilePic     string `db:"profile_picture" json:"profile_picture"`
	PasswordHash   string `db:"password_hash" json:"-"`
	GoogleHandle   string `db:"google_handle" json:"-"`
	FacebookHandle string `db:"facebook_handle" json:"-"`
	Status         string `db:"status" json:"status"`
	RoleID         int    `db:"role_id" json:"role_id"`
	CreatedAt      string `db:"created_at" json:"created_at"`
	UpdatedAt      string `db:"updated_at" json:"updated_at"`
}

type AccountRole struct {
	AccountID   string `db:"account_id" json:"account_id"`
	RoleID      int    `db:"role_id" json:"role_id"`
	RoleName    string `db:"role_name" json:"role_name"`
	Permissions int64  `db:"permissions" json:"permissions"`
}
