-- migrate:up

CREATE extension IF NOT EXISTS "uuid-ossp";

CREATE TYPE "account_status" AS ENUM (
  'unverified',
  'verified'
);

CREATE TABLE "roles" (
  "id" SERIAL PRIMARY KEY,
  "name" VARCHAR(255) NOT NULL,
  "permissions" BIGINT NOT NULL DEFAULT 0,
  "created_at" timestamp NOT NULL DEFAULT now(),
  "updated_at" timestamp NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX "roles_name_unique" ON "roles" ("name");

CREATE TABLE "accounts" (
  "id" UUID PRIMARY KEY  DEFAULT uuid_generate_v4(),
  "email" VARCHAR(255) NOT NULL,
  "full_name" VARCHAR(255) NOT NULL,
  "profile_picture" TEXT NOT NULL default '',
  "password_hash" VARCHAR(255) NOT NULL,
  "google_handle" VARCHAR(255) NOT NULL,
  "facebook_handle" VARCHAR(255) NOT NULL,
  "status" "account_status" NOT NULL DEFAULT 'unverified',
  "role_id" INTEGER NOT NULL REFERENCES "roles" ("id"),
  "created_at" timestamp NOT NULL DEFAULT now(),
  "updated_at" timestamp NOT NULL DEFAULT now()
);

-- Insert default roles
INSERT INTO "roles" ("name", "permissions") VALUES ('client', 0);
INSERT INTO "roles" ("name", "permissions") VALUES ('designer', 1);
INSERT INTO "roles" ("name", "permissions") VALUES ('manager', 1);
INSERT INTO "roles" ("name", "permissions") VALUES ('admin', 1);

-- migrate:down
DROP TABLE "accounts";
DROP TABLE "roles";
DROP TYPE "account_status";

