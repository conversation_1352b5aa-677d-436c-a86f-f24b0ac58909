version: '3'
services:
  pinktool_db:
    image: pgvector/pgvector:pg17
    container_name: pinktool_db
    environment:
      POSTGRES_DB: pinktool_db
      POSTGRES_USER: pinktool
      POSTGRES_PASSWORD: pinktool
      PGDATA: /data/pgdata
    volumes:
      - ./data:/data:cached
    ports:
      - "6600:5432"
    networks:
      - default
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 1s
      timeout: 2s
      retries: 10

  pinktool_consul:
    image: consul:1.15.4
    container_name: consul
    environment:
      - 'CONSUL_LOCAL_CONFIG={"server_rejoin_age_max": "720h"}'
    ports:
      - "8500:8500"               # Consul HTTP API
      - "8600:8600/udp"           # DNS Interface
    command: "agent -server -ui -data-dir=/consul/data -bootstrap-expect=1 -client=0.0.0.0"
    # command: "agent -data-dir=/consul/data -bootstrap-expect=1 -server -ui -client=0.0.0.0"
    networks:
      - default
    volumes:
      - ./data/consul:/consul/data
