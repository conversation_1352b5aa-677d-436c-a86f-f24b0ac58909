{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["./src/api/*", "./src/openapi/*"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"complexity": {"noThisInStatic": "off", "noStaticOnlyClass": "off", "noForEach": "off", "useArrowFunction": "off"}, "recommended": true, "style": {"useImportType": "off", "useSelfClosingElements": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off", "noPrototypeBuiltins": "off"}, "a11y": {"useKeyWithClickEvents": "off", "noSvgWithoutTitle": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}