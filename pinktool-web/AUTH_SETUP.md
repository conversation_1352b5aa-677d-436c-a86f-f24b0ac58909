# Authentication Setup Guide

This guide covers the complete authentication system implementation including login, signup, forgot password, and OAuth integration with Google and Facebook.

## Features Implemented

✅ **Email Authentication**
- Clean login/signup forms with validation
- Password strength requirements
- Form error handling with React Hook Form

✅ **Forgot Password Flow**
- Email-based password reset
- Reset token validation
- Success/error states

✅ **OAuth Integration (Ready for Configuration)**
- Google OAuth 2.0 setup
- Facebook OAuth setup
- Callback handling
- Error management

✅ **State Management**
- Zustand store for auth state
- Persistent authentication
- User profile management

✅ **UI/UX**
- Responsive design with Mantine UI
- Clean, modern interface
- Loading states and feedback
- Consistent styling with Panda CSS

## File Structure

```
src/
├── routes/
│   ├── account/
│   │   ├── login.tsx           # Login page
│   │   ├── signup.tsx          # Signup page
│   │   ├── forgot-password.tsx # Forgot password
│   │   └── reset-password.tsx  # Reset password
│   └── auth/
│       ├── google/
│       │   └── callback.tsx    # Google OAuth callback
│       └── facebook/
│           └── callback.tsx    # Facebook OAuth callback
├── stores/
│   └── authStore.ts           # Authentication state management
└── utils/
    └── auth.ts                # Auth utilities and OAuth helpers
```

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Update the values:
```env
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_FACEBOOK_APP_ID=your_facebook_app_id_here
VITE_API_BASE_URL=http://localhost:3000/api
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:5173/auth/google/callback` (development)
   - `https://yourdomain.com/auth/google/callback` (production)
6. Copy Client ID to `.env`

### 3. Facebook OAuth Setup

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `http://localhost:5173/auth/facebook/callback` (development)
   - `https://yourdomain.com/auth/facebook/callback` (production)
5. Copy App ID to `.env`

### 4. Enable OAuth in Components

Uncomment OAuth handlers in login/signup pages:

```typescript
// In src/routes/account/login.tsx and signup.tsx
import { handleGoogleAuth, handleFacebookAuth } from "../../utils/auth";

const handleGoogleLogin = () => {
  handleGoogleAuth(); // Uncomment this line
};

const handleFacebookLogin = () => {
  handleFacebookAuth(); // Uncomment this line
};
```

## Backend Integration

The frontend is ready for backend integration. You'll need to implement:

### API Endpoints

```typescript
// Authentication endpoints
POST /api/auth/login
POST /api/auth/signup  
POST /api/auth/forgot-password
POST /api/auth/reset-password
POST /api/auth/google
POST /api/auth/facebook
POST /api/auth/refresh
POST /api/auth/logout

// User endpoints
GET /api/user/profile
PUT /api/user/profile
```

### Example API Integration

Update the form handlers in your components:

```typescript
// In login.tsx
const onSubmit = async (data: LoginForm) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    
    if (response.ok) {
      setAuth(result.user, result.token);
      navigate({ to: '/' });
    } else {
      // Handle error
    }
  } catch (error) {
    // Handle error
  }
};
```

## Security Considerations

1. **HTTPS Required**: OAuth providers require HTTPS in production
2. **Token Storage**: Tokens are stored in localStorage (consider httpOnly cookies for production)
3. **CSRF Protection**: Implement CSRF tokens for state parameter in OAuth
4. **Rate Limiting**: Implement rate limiting on auth endpoints
5. **Input Validation**: Server-side validation is required
6. **Password Hashing**: Use bcrypt or similar for password hashing

## Testing

Test the authentication flow:

1. **Email Signup/Login**
   - Navigate to `/account/signup`
   - Fill form with valid data
   - Check validation errors
   - Test login flow

2. **Forgot Password**
   - Navigate to `/account/forgot-password`
   - Enter email
   - Check success state

3. **OAuth (after configuration)**
   - Click Google/Facebook buttons
   - Verify redirect to OAuth provider
   - Test callback handling

## Customization

### Styling
- Modify Panda CSS classes in components
- Update color scheme in `panda.config.ts`
- Customize Mantine theme in `main.tsx`

### Validation
- Update validation rules in form components
- Modify password strength requirements in `utils/auth.ts`

### OAuth Providers
- Add more providers by extending `utils/auth.ts`
- Create additional callback routes

## Troubleshooting

**OAuth not working:**
- Check environment variables
- Verify redirect URIs match exactly
- Ensure HTTPS in production

**Form validation issues:**
- Check React Hook Form setup
- Verify validation rules
- Test error display

**State not persisting:**
- Check Zustand persist configuration
- Verify localStorage access
- Test auth store actions

## Next Steps

1. Implement backend API endpoints
2. Add email verification flow
3. Implement two-factor authentication
4. Add social login with more providers
5. Implement session management
6. Add audit logging