import { TOKEN_KEY } from "./constants";
import logger from "./logger";

type StoreValue = {
  [TOKEN_KEY]: string;
  // Sidebar toggle
  sb: boolean;
};

class Store<T extends StoreValue> {
  private static _instance: Store<StoreValue>;
  private constructor() {}

  static get Instance() {
    const ret = this._instance ?? new this();

    return ret;
  }

  set<U extends keyof T>(key: U, value: T[U]) {
    localStorage.setItem(key as string, JSON.stringify(value));
  }

  delete<U extends keyof T>(key: U): void {
    localStorage.removeItem(key as string);
  }

  get<U extends keyof T>(key: U): T[U] | null {
    const v = localStorage.getItem(key as string);
    if (v) {
      try {
        const ret = JSON.parse(v);
        return ret;
      } catch (err) {
        logger.error("Store can not parse value", err);
        return null;
      }
    }

    return null;
  }
}

const store = Store.Instance;

export default store;
