// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { GetMeData, GetMeResponses, GetMeErrors, GetClientsData, GetClientsResponses, GetClientsErrors, AdminUpdateClientData, AdminUpdateClientResponses, AdminUpdateClientErrors, LoginData, LoginResponses, LoginErrors, SignupData, SignupResponses, SignupErrors, PingData, PingResponses, PingErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export class AccountService {
    public static getMe<ThrowOnError extends boolean = false>(options?: Options<GetMeData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<GetMeResponses, GetMeErrors, ThrowOnError>({
            url: '/account/me',
            ...options
        });
    }
}

export class AdminService {
    public static getClients<ThrowOnError extends boolean = false>(options?: Options<GetClientsData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<GetClientsResponses, GetClientsErrors, ThrowOnError>({
            url: '/admin/clients',
            ...options
        });
    }
    
    public static adminUpdateClient<ThrowOnError extends boolean = false>(options: Options<AdminUpdateClientData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).put<AdminUpdateClientResponses, AdminUpdateClientErrors, ThrowOnError>({
            url: '/admin/clients/{client_id}',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    }
}

export class AuthService {
    public static login<ThrowOnError extends boolean = false>(options: Options<LoginData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<LoginResponses, LoginErrors, ThrowOnError>({
            url: '/auth/login',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    }
    
    public static signup<ThrowOnError extends boolean = false>(options: Options<SignupData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<SignupResponses, SignupErrors, ThrowOnError>({
            url: '/auth/signup',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    }
}

export class HealthcheckService {
    public static ping<ThrowOnError extends boolean = false>(options?: Options<PingData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<PingResponses, PingErrors, ThrowOnError>({
            url: '/healthcheck/ping',
            ...options
        });
    }
}