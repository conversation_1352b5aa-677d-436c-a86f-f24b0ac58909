// This file is auto-generated by @hey-api/openapi-ts

export type Account = {
    created_at: string;
    email: string;
    full_name: string;
    id: string;
    profile_picture: string;
    role_id: number;
    status: string;
    updated_at: string;
};

export type ErrorDetail = {
    /**
     * Where the error occurred, e.g. 'body.items[3].tags' or 'path.thing-id'
     */
    location?: string;
    /**
     * Error message text
     */
    message?: string;
    /**
     * The value at the given location
     */
    value?: unknown;
};

export type ErrorModel = {
    /**
     * A human-readable explanation specific to this occurrence of the problem.
     */
    detail?: string;
    /**
     * Optional list of individual error details
     */
    errors?: Array<ErrorDetail> | null;
    /**
     * A URI reference that identifies the specific occurrence of the problem.
     */
    instance?: string;
    /**
     * HTTP status code
     */
    status?: number;
    /**
     * A short, human-readable summary of the problem type. This value should not change between occurrences of the error.
     */
    title?: string;
    /**
     * A URI reference to human-readable documentation for the error.
     */
    type?: string;
};

export type GetMeResponseBody = {
    created_at: string;
    email: string;
    full_name: string;
    id: string;
    profile_picture: string;
    role_id: number;
    role_name: string;
    status: string;
    updated_at: string;
};

export type HandleGetClientsResponseBody = {
    current_page: number;
    entries: Array<Account> | null;
    total_entries: number;
};

export type HandleUpdateClientInputBody = {
    status: string;
};

export type HandleUpdateClientResponseBody = {
    message: string;
};

export type LoginInputBody = {
    email: string;
    password: string;
};

export type LoginOutputBody = {
    token: string;
};

export type PingOutputBody = {
    message: string;
};

export type SignupInputBody = {
    confirm_password: string;
    email: string;
    full_name: string;
    password: string;
};

export type SignupOutputBody = {
    created_at: string;
    email: string;
    full_name: string;
    id: string;
    updated_at: string;
};

export type GetMeData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/account/me';
};

export type GetMeErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type GetMeError = GetMeErrors[keyof GetMeErrors];

export type GetMeResponses = {
    /**
     * OK
     */
    200: GetMeResponseBody;
};

export type GetMeResponse = GetMeResponses[keyof GetMeResponses];

export type GetClientsData = {
    body?: never;
    path?: never;
    query?: {
        limit?: number;
        page?: number;
    };
    url: '/admin/clients';
};

export type GetClientsErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type GetClientsError = GetClientsErrors[keyof GetClientsErrors];

export type GetClientsResponses = {
    /**
     * OK
     */
    200: HandleGetClientsResponseBody;
};

export type GetClientsResponse = GetClientsResponses[keyof GetClientsResponses];

export type AdminUpdateClientData = {
    body: HandleUpdateClientInputBody;
    path: {
        client_id: string;
    };
    query?: never;
    url: '/admin/clients/{client_id}';
};

export type AdminUpdateClientErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type AdminUpdateClientError = AdminUpdateClientErrors[keyof AdminUpdateClientErrors];

export type AdminUpdateClientResponses = {
    /**
     * OK
     */
    200: HandleUpdateClientResponseBody;
};

export type AdminUpdateClientResponse = AdminUpdateClientResponses[keyof AdminUpdateClientResponses];

export type LoginData = {
    body: LoginInputBody;
    path?: never;
    query?: never;
    url: '/auth/login';
};

export type LoginErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type LoginError = LoginErrors[keyof LoginErrors];

export type LoginResponses = {
    /**
     * OK
     */
    200: LoginOutputBody;
};

export type LoginResponse = LoginResponses[keyof LoginResponses];

export type SignupData = {
    body: SignupInputBody;
    path?: never;
    query?: never;
    url: '/auth/signup';
};

export type SignupErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type SignupError = SignupErrors[keyof SignupErrors];

export type SignupResponses = {
    /**
     * OK
     */
    200: SignupOutputBody;
};

export type SignupResponse = SignupResponses[keyof SignupResponses];

export type PingData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/healthcheck/ping';
};

export type PingErrors = {
    /**
     * Error
     */
    default: ErrorModel;
};

export type PingError = PingErrors[keyof PingErrors];

export type PingResponses = {
    /**
     * OK
     */
    200: PingOutputBody;
};

export type PingResponse = PingResponses[keyof PingResponses];

export type ClientOptions = {
    baseUrl: 'http://localhost:4200' | (string & {});
};