import { Box, Text, Stack, Group, UnstyledButton, Badge, Tooltip } from "@mantine/core";
import { Link, useLocation } from "@tanstack/react-router";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";
import { TbCoin, TbHome, TbNotification, TbSettings } from "react-icons/tb";
import { css } from "styled-system/css";

interface SidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const navigationItems = [
  { icon: TbHome, label: "Dashboard", path: "/" },
  {
    icon: TbNotification,
    label: "Notifications",
    path: "/notifications",
    badge: 4,
  },
];

const sisyphusVenturesItems = [{ icon: TbCoin, label: "Payments", path: "/payments" }];

const bottomItems = [{ icon: TbSettings, label: "Settings", path: "/settings" }];

export function Sidebar({ collapsed = false, onToggle }: SidebarProps) {
  const location = useLocation();

  const NavItem = ({ icon: Icon, label, path, active = false, badge, external = false }: any) => {
    const buttonStyles = css({
      display: "flex",
      alignItems: "center",
      gap: collapsed ? "0" : "12px",
      padding: collapsed ? "10px" : "10px 12px",
      borderRadius: "8px",
      width: "100%",
      color: active ? "#3B82F6" : "#6B7280",
      backgroundColor: active ? "#EFF6FF" : "transparent",
      fontSize: "14px",
      fontWeight: active ? "600" : "500",
      transition: "all 0.2s ease",
      justifyContent: collapsed ? "center" : "flex-start",
      minHeight: "32px",
      "&:hover": {
        backgroundColor: active ? "#EFF6FF" : "#F9FAFB",
        color: active ? "#3B82F6" : "#374151",
      },
    });

    const button = external ? (
      <UnstyledButton
        component="a"
        href={path}
        target="_blank"
        className={buttonStyles}
      >
        <Icon size={18} />
        {!collapsed && (
          <>
            <Text size="sm" style={{ flex: 1 }}>
              {label}
            </Text>
            {badge && (
              <Badge size="xs" color="blue" variant="filled">
                {badge}
              </Badge>
            )}
            <Text size="xs">↗</Text>
          </>
        )}
      </UnstyledButton>
    ) : (
      <UnstyledButton
        component={Link}
        to={path}
        className={buttonStyles}
      >
        <Icon size={18} />
        {!collapsed && (
          <>
            <Text size="sm" style={{ flex: 1 }}>
              {label}
            </Text>
            {badge && (
              <Badge size="xs" color="blue" variant="filled">
                {badge}
              </Badge>
            )}
          </>
        )}
      </UnstyledButton>
    );

    if (collapsed) {
      return (
        <Tooltip label={label} position="right" withArrow>
          {button}
        </Tooltip>
      );
    }

    return button;
  };

  return (
    <Box
      className={css({
        width: collapsed ? "60px" : "220px",
        height: "100vh",
        backgroundColor: "white",
        borderRight: "1px solid #E5E7EB",
        display: "flex",
        flexDirection: "column",
        transition: "width 0.3s ease",
        flexShrink: 0,
      })}
    >
      {/* Header */}
      <Box
        className={css({
          padding: collapsed ? "16px 8px" : "16px",
          borderBottom: "1px solid #E5E7EB",
          display: "flex",
          alignItems: "center",
          justifyContent: collapsed ? "center" : "space-between",
          minHeight: "64px",
        })}
      >
        {!collapsed && (
          <Group gap="xs">
            <Box
              className={css({
                width: "24px",
                height: "24px",
                backgroundColor: "#1F2937",
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              })}
            >
              <Text size="xs" fw={700} c="white">
                U
              </Text>
            </Box>
            <Text fw={600} size="sm" c="#1F2937">
              Untitled UI
            </Text>
            <Badge size="xs" variant="light" color="gray">
              v3.0
            </Badge>
          </Group>
        )}

        {collapsed && (
          <Box
            className={css({
              width: "24px",
              height: "24px",
              backgroundColor: "#1F2937",
              borderRadius: "6px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: "8px",
            })}
          >
            <Text size="xs" fw={700} c="white">
              U
            </Text>
          </Box>
        )}

        {!collapsed && (
          <UnstyledButton
            onClick={onToggle}
            className={css({
              padding: "4px",
              borderRadius: "4px",
              color: "#6B7280",
              "&:hover": {
                backgroundColor: "#F3F4F6",
              },
            })}
          >
            <HiChevronLeft size={16} />
          </UnstyledButton>
        )}
      </Box>

      {/* Collapsed Toggle Button */}
      {collapsed && (
        <Box
          className={css({
            padding: "8px",
            display: "flex",
            justifyContent: "center",
            borderBottom: "1px solid #E5E7EB",
          })}
        >
          <UnstyledButton
            onClick={onToggle}
            className={css({
              padding: "4px",
              borderRadius: "4px",
              color: "#6B7280",
              "&:hover": {
                backgroundColor: "#F3F4F6",
              },
            })}
          >
            <HiChevronRight size={16} />
          </UnstyledButton>
        </Box>
      )}

      {/* Navigation */}
      <Box
        className={css({
          flex: 1,
          padding: collapsed ? "16px 8px" : "16px 12px",
          overflow: "hidden",
        })}
      >
        {/* General Section */}
        {!collapsed && (
          <Text size="xs" fw={600} c="#6B7280" tt="uppercase" mb="sm">
            GENERAL
          </Text>
        )}
        <Stack gap="sm" mb={collapsed ? "lg" : "xl"}>
          {navigationItems.map((item) => (
            <NavItem key={item.path} {...item} active={location.pathname === item.path} />
          ))}
        </Stack>

        {/* Sisyphus Ventures Section */}
        {!collapsed && (
          <Text size="xs" fw={600} c="#6B7280" tt="uppercase" mb="sm">
            SISYPHUS VENTURES
          </Text>
        )}
        {collapsed && (
          <Box
            className={css({
              height: "1px",
              backgroundColor: "#E5E7EB",
              margin: "16px 0",
            })}
          />
        )}
        <Stack gap="sm">
          {sisyphusVenturesItems.map((item) => (
            <NavItem key={item.path} {...item} active={location.pathname === item.path} />
          ))}
        </Stack>
      </Box>

      {/* Bottom Section */}
      <Box className={css({ padding: "12px", borderTop: "1px solid #E5E7EB" })}>
        <Stack gap="sm">
          {bottomItems.map((item) => (
            <NavItem key={item.path} {...item} active={location.pathname === item.path} />
          ))}
        </Stack>
      </Box>
    </Box>
  );
}
