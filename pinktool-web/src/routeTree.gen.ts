/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as UserManagementRouteImport } from './routes/user-management'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AccountSignupRouteImport } from './routes/account/signup'
import { Route as AccountResetPasswordRouteImport } from './routes/account/reset-password'
import { Route as AccountLoginRouteImport } from './routes/account/login'
import { Route as AccountForgotPasswordRouteImport } from './routes/account/forgot-password'
import { Route as Dashboard_layoutDashboard_layoutRouteImport } from './routes/_dashboard_layout/_dashboard_layout'
import { Route as AuthGoogleCallbackRouteImport } from './routes/auth/google/callback'
import { Route as AuthFacebookCallbackRouteImport } from './routes/auth/facebook/callback'

const UserManagementRoute = UserManagementRouteImport.update({
  id: '/user-management',
  path: '/user-management',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountSignupRoute = AccountSignupRouteImport.update({
  id: '/account/signup',
  path: '/account/signup',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountResetPasswordRoute = AccountResetPasswordRouteImport.update({
  id: '/account/reset-password',
  path: '/account/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountLoginRoute = AccountLoginRouteImport.update({
  id: '/account/login',
  path: '/account/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AccountForgotPasswordRoute = AccountForgotPasswordRouteImport.update({
  id: '/account/forgot-password',
  path: '/account/forgot-password',
  getParentRoute: () => rootRouteImport,
} as any)
const Dashboard_layoutDashboard_layoutRoute =
  Dashboard_layoutDashboard_layoutRouteImport.update({
    id: '/_dashboard_layout/_dashboard_layout',
    getParentRoute: () => rootRouteImport,
  } as any)
const AuthGoogleCallbackRoute = AuthGoogleCallbackRouteImport.update({
  id: '/auth/google/callback',
  path: '/auth/google/callback',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthFacebookCallbackRoute = AuthFacebookCallbackRouteImport.update({
  id: '/auth/facebook/callback',
  path: '/auth/facebook/callback',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/user-management': typeof UserManagementRoute
  '/account/forgot-password': typeof AccountForgotPasswordRoute
  '/account/login': typeof AccountLoginRoute
  '/account/reset-password': typeof AccountResetPasswordRoute
  '/account/signup': typeof AccountSignupRoute
  '/auth/facebook/callback': typeof AuthFacebookCallbackRoute
  '/auth/google/callback': typeof AuthGoogleCallbackRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/user-management': typeof UserManagementRoute
  '/account/forgot-password': typeof AccountForgotPasswordRoute
  '/account/login': typeof AccountLoginRoute
  '/account/reset-password': typeof AccountResetPasswordRoute
  '/account/signup': typeof AccountSignupRoute
  '/auth/facebook/callback': typeof AuthFacebookCallbackRoute
  '/auth/google/callback': typeof AuthGoogleCallbackRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/user-management': typeof UserManagementRoute
  '/_dashboard_layout/_dashboard_layout': typeof Dashboard_layoutDashboard_layoutRoute
  '/account/forgot-password': typeof AccountForgotPasswordRoute
  '/account/login': typeof AccountLoginRoute
  '/account/reset-password': typeof AccountResetPasswordRoute
  '/account/signup': typeof AccountSignupRoute
  '/auth/facebook/callback': typeof AuthFacebookCallbackRoute
  '/auth/google/callback': typeof AuthGoogleCallbackRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/user-management'
    | '/account/forgot-password'
    | '/account/login'
    | '/account/reset-password'
    | '/account/signup'
    | '/auth/facebook/callback'
    | '/auth/google/callback'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/user-management'
    | '/account/forgot-password'
    | '/account/login'
    | '/account/reset-password'
    | '/account/signup'
    | '/auth/facebook/callback'
    | '/auth/google/callback'
  id:
    | '__root__'
    | '/'
    | '/user-management'
    | '/_dashboard_layout/_dashboard_layout'
    | '/account/forgot-password'
    | '/account/login'
    | '/account/reset-password'
    | '/account/signup'
    | '/auth/facebook/callback'
    | '/auth/google/callback'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  UserManagementRoute: typeof UserManagementRoute
  Dashboard_layoutDashboard_layoutRoute: typeof Dashboard_layoutDashboard_layoutRoute
  AccountForgotPasswordRoute: typeof AccountForgotPasswordRoute
  AccountLoginRoute: typeof AccountLoginRoute
  AccountResetPasswordRoute: typeof AccountResetPasswordRoute
  AccountSignupRoute: typeof AccountSignupRoute
  AuthFacebookCallbackRoute: typeof AuthFacebookCallbackRoute
  AuthGoogleCallbackRoute: typeof AuthGoogleCallbackRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/user-management': {
      id: '/user-management'
      path: '/user-management'
      fullPath: '/user-management'
      preLoaderRoute: typeof UserManagementRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account/signup': {
      id: '/account/signup'
      path: '/account/signup'
      fullPath: '/account/signup'
      preLoaderRoute: typeof AccountSignupRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account/reset-password': {
      id: '/account/reset-password'
      path: '/account/reset-password'
      fullPath: '/account/reset-password'
      preLoaderRoute: typeof AccountResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account/login': {
      id: '/account/login'
      path: '/account/login'
      fullPath: '/account/login'
      preLoaderRoute: typeof AccountLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/account/forgot-password': {
      id: '/account/forgot-password'
      path: '/account/forgot-password'
      fullPath: '/account/forgot-password'
      preLoaderRoute: typeof AccountForgotPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_dashboard_layout/_dashboard_layout': {
      id: '/_dashboard_layout/_dashboard_layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof Dashboard_layoutDashboard_layoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/google/callback': {
      id: '/auth/google/callback'
      path: '/auth/google/callback'
      fullPath: '/auth/google/callback'
      preLoaderRoute: typeof AuthGoogleCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/facebook/callback': {
      id: '/auth/facebook/callback'
      path: '/auth/facebook/callback'
      fullPath: '/auth/facebook/callback'
      preLoaderRoute: typeof AuthFacebookCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  UserManagementRoute: UserManagementRoute,
  Dashboard_layoutDashboard_layoutRoute: Dashboard_layoutDashboard_layoutRoute,
  AccountForgotPasswordRoute: AccountForgotPasswordRoute,
  AccountLoginRoute: AccountLoginRoute,
  AccountResetPasswordRoute: AccountResetPasswordRoute,
  AccountSignupRoute: AccountSignupRoute,
  AuthFacebookCallbackRoute: AuthFacebookCallbackRoute,
  AuthGoogleCallbackRoute: AuthGoogleCallbackRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
