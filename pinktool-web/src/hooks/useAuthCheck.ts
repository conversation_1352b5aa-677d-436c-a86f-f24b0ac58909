import { useCallback, useEffect } from "react";
import { useAccount } from "./useAccount";
import { AccountService } from "@/api";
import logger from "@/pkg/logger";
import store from "@/pkg/store";
import { TOKEN_KEY } from "@/pkg/constants";
import { useNavigate } from "@tanstack/react-router";

const useAuthCheck = () => {
  const account = useAccount((state) => state.account);
  const setAccount = useAccount((state) => state.setAccount);
  const navigate = useNavigate();
  console.log("account", account);

  const logout = useCallback(
    (err: Error) => {
      logger.error(err);
      store.delete(TOKEN_KEY);
      navigate({ to: "/account/login" });
    },
    [navigate],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Only need to check account once
  useEffect(() => {
    console.log("useAuthCheck", account);
    if (!account) {
      AccountService.getMe().then((res) => {
        if (res.data) {
          if (res.data.status === "unverified") {
            navigate({ to: "/account/verify" });
            return;
          }
          setAccount({
            id: res.data.id,
            email: res.data.email,
            profilePic: res.data.profile_picture,
            role: res.data.role_name,
            status: res.data.status,
            fullName: res.data.full_name,
          });
        } else {
          logout(new Error("No account found"));
        }
      });
    } else {
      console.log("useAuthCheck", account.status);
      if (account.status === "unverified") {
        navigate({ to: "/account/verify" });
      }
    }
  }, []);
};

export default useAuthCheck;
