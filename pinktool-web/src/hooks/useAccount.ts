import { client } from "@/api/client.gen";
import { TOKEN_KEY } from "@/pkg/constants";
import store from "@/pkg/store";
import { create } from "zustand";

export type Account = {
  id: string;
  email: string;
  profilePic: string;
  fullName: string;
  role: string;
  status: string;
};

type AccountStore = {
  account: Account;
  token: string;
  setAccount: (account: Partial<Account>) => void;
  setToken: (token: string) => void;
};

export const useAccount = create<AccountStore>((set, get) => ({
  account: null as any,
  token: "",
  setAccount: (account: Partial<Account>) => {
    set({ account: { ...get().account, ...account } as Account });
  },
  setToken: (token: string) => {
    set({ token });
    store.set(TOKEN_KEY, token);

    client.interceptors.request.use((request) => {
      request.headers.set("Authorization", `Bearer ${token}`);
      return request;
    });
  },
}));
