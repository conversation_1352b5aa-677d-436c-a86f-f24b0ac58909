import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import {
  TextInput,
  Button,
  Title,
  Text,
  Anchor,
  Stack,
  Container,
  Center,
  Alert,
  Box,
  Group,
} from "@mantine/core";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { FaCheckCircle, FaArrowLeft } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import { css } from "styled-system/css";

export const Route = createFileRoute("/account/forgot-password")({
  component: RouteComponent,
});

interface ForgotPasswordForm {
  email: string;
}

function RouteComponent() {
  const [isEmailSent, setIsEmailSent] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    getValues,
  } = useForm<ForgotPasswordForm>();

  const onSubmit = async (data: ForgotPasswordForm) => {
    // TODO: Implement forgot password logic
    console.log("Forgot password data:", data);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsEmailSent(true);
  };

  if (isEmailSent) {
    return (
      <div
        className={css({
          minHeight: "100vh",
          background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        })}
      >
        <Container size="sm">
          <Center>
            <Box
              className={css({
                background: "white",
                borderRadius: "24px",
                padding: "60px 40px",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                maxWidth: "500px",
                width: "100%",
                textAlign: "center",
              })}
            >
              <Center mb="xl">
                <FaCheckCircle
                  size={48}
                  className={css({ color: "#10b981" })}
                />
              </Center>
              
              <Title
                ta="center"
                className={css({
                  fontSize: "28px",
                  fontWeight: 700,
                  marginBottom: "16px",
                  color: "gray.900",
                })}
              >
                Check your email
              </Title>

              <Text c="gray.6" size="sm" ta="center" mb="xl">
                We've sent a password reset link to{" "}
                <Text span fw={500} c="gray.8">
                  {getValues("email")}
                </Text>
              </Text>

              <Alert color="blue" variant="light" mb="xl">
                <Text size="sm">
                  Didn't receive the email? Check your spam folder or try again in a few minutes.
                </Text>
              </Alert>

              <Stack gap="md">
                <Button
                  variant="outline"
                  fullWidth
                  leftSection={<FaArrowLeft />}
                  component={Link}
                  to="/account/login"
                  size="md"
                  className={css({
                    borderColor: "#ff6b6b",
                    color: "#ff6b6b",
                    height: "48px",
                    _hover: { backgroundColor: "#fef2f2" },
                  })}
                >
                  Back to login
                </Button>
                
                <Button
                  variant="subtle"
                  fullWidth
                  onClick={() => setIsEmailSent(false)}
                  size="md"
                  className={css({
                    color: "gray.600",
                    height: "48px",
                    _hover: { backgroundColor: "gray.50" },
                  })}
                >
                  Try different email
                </Button>
              </Stack>
            </Box>
          </Center>
        </Container>
      </div>
    );
  }

  return (
    <div
      className={css({
        minHeight: "100vh",
        background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px",
      })}
    >
      <Container size="sm">
        <Center>
          <Box
            className={css({
              background: "white",
              borderRadius: "24px",
              padding: "60px 40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              maxWidth: "500px",
              width: "100%",
            })}
          >
            {/* Logo */}
            <Group mb="xl" justify="center">
              <div
                className={css({
                  width: "32px",
                  height: "32px",
                  background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                  borderRadius: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                })}
              >
                <Text c="white" fw={700} size="lg">
                  R
                </Text>
              </div>
              <Text fw={600} size="xl" c="gray.8">
                Recova
              </Text>
            </Group>

            <Title
              ta="center"
              className={css({
                fontSize: "28px",
                fontWeight: 700,
                marginBottom: "16px",
                color: "gray.900",
              })}
            >
              Forgot your password?
            </Title>

            <Text c="gray.6" size="sm" ta="center" mb="xl">
              Enter your email address and we'll send you a link to reset your password.
            </Text>

            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack gap="md">
                <TextInput
                  leftSection={<MdEmail color="#9ca3af" />}
                  placeholder="<EMAIL>"
                  size="md"
                  error={errors.email?.message}
                  className={css({
                    "& input": {
                      height: "48px",
                      border: "1px solid #e9ecef",
                      _focus: { borderColor: "#ff6b6b" },
                    },
                  })}
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: "Invalid email address",
                    },
                  })}
                />

                <Button
                  type="submit"
                  fullWidth
                  size="md"
                  loading={isSubmitting}
                  className={css({
                    background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                    border: "none",
                    color: "white",
                    height: "48px",
                    _hover: { opacity: 0.9 },
                  })}
                >
                  Send reset link
                </Button>
              </Stack>
            </form>

            <Text c="gray.6" size="sm" ta="center" mt="lg">
              Remember your password?{" "}
              <Anchor
                component={Link}
                to="/account/login"
                className={css({ color: "#ff6b6b", textDecoration: "none" })}
              >
                Back to login
              </Anchor>
            </Text>
          </Box>
        </Center>
      </Container>
    </div>
  );
}