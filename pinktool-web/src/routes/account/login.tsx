import { create<PERSON>ile<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "@tanstack/react-router";
import {
  TextInput,
  Button,
  Title,
  Text,
  Anchor,
  Stack,
  Group,
  Container,
  Center,
  Box,
  PasswordInput,
  Alert,
} from "@mantine/core";
import { useForm } from "react-hook-form";
import { FcGoogle } from "react-icons/fc";
import { SiFacebook } from "react-icons/si";
import { MdEmail, MdLock, MdError } from "react-icons/md";
import { css } from "styled-system/css";
import { AuthService } from "@/api/sdk.gen";
import { useState, useEffect } from "react";
import { googleAuthService, type GoogleUserInfo } from "@/services/googleAuth";
import { useAccount } from "@/hooks/useAccount";

export const Route = createFileRoute("/account/login")({
  component: RouteComponent,
});

interface LoginForm {
  email: string;
  password: string;
}

function RouteComponent() {
  const navigate = useNavigate();
  const setToken = useAccount((state) => state.setToken);
  const [apiError, setApiError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginForm>();

  // Initialize Google OAuth when component mounts
  useEffect(() => {
    googleAuthService.initialize(() => {});
  }, []);

  const onSubmit = async (data: LoginForm) => {
    try {
      setApiError(null);
      const response = await AuthService.login({
        body: {
          email: data.email,
          password: data.password,
        },
      });

      if (response.data) {
        setToken(response.data.token);
        navigate({ to: "/" });
      }
    } catch (error: any) {
      console.error("Login error:", error);
      setApiError(error?.body?.message || "Login failed. Please check your credentials.");
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setApiError(null);

      await googleAuthService.initialize(async (userInfo: GoogleUserInfo) => {
        try {
          // Try to login with Google account
          const response = await AuthService.login({
            body: {
              email: userInfo.email,
              password: `google_${userInfo.sub}`, // Use Google ID as password
            },
          });

          if (response.data?.token) {
            // login(response.data.token, {
            //   id: userInfo.sub,
            //   email: userInfo.email,
            //   full_name: userInfo.name,
            // });
            navigate({ to: "/" });
          }
        } catch (error: any) {
          // If login fails, suggest signup
          if (error?.status === 401 || error?.body?.message?.includes("Invalid credentials")) {
            setApiError("Google account not found. Please sign up first or use manual login.");
          } else {
            setApiError(error?.body?.message || "Google login failed. Please try again.");
          }
        }
      });

      // Trigger Google sign-in
      googleAuthService.prompt();
    } catch (error) {
      console.error("Google login error:", error);
      setApiError("Google authentication failed. Please try again.");
    }
  };

  const handleMicrosoftLogin = () => {
    // TODO: Implement Microsoft OAuth
    console.log("Microsoft login");
  };

  return (
    <div
      className={css({
        minHeight: "100vh",
        background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px",
        "@media (max-width: 768px)": {
          padding: "10px",
        },
      })}
    >
      <Container size="lg" w="100%">
        <Center>
          <div
            className={css({
              display: "flex",
              flexDirection: "row",
              background: "white",
              borderRadius: "24px",
              overflow: "hidden",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              maxWidth: "900px",
              width: "100%",
              minHeight: "600px",
              "@media (max-width: 768px)": {
                flexDirection: "column",
                borderRadius: "16px",
                maxWidth: "400px",
                minHeight: "auto",
              },
            })}
          >
            {/* Left side - Image */}
            <Box
              className={css({
                flex: 1,
                animation: "gradient 15s ease infinite",
                position: "relative",
                w: "96",
                display: "block",
                minHeight: "600px",
                "@media (max-width: 768px)": {
                  display: "none",
                },
              })}
            >
              <div
                className={css({
                  position: "absolute",
                  inset: 0,
                })}
                style={{
                  backgroundImage: "url(/images/login_pic.png)",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              />
            </Box>

            {/* Right side - Form */}
            <Box
              className={css({
                flex: 1,
                padding: "60px 40px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                minWidth: "400px",
                width: "auto",
                "@media (max-width: 768px)": {
                  padding: "30px 20px",
                  minWidth: "100%",
                  width: "100%",
                },
              })}
            >
              {/* Logo */}
              <Group
                mb="xl"
                justify="center"
                className={css({
                  "@media (max-width: 768px)": { marginBottom: "lg" },
                })}
              >
                <div
                  className={css({
                    width: "32px",
                    height: "32px",
                    background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    "@media (max-width: 768px)": {
                      width: "28px",
                      height: "28px",
                    },
                  })}
                >
                  <Text c="white" fw={700} size="lg">
                    P
                  </Text>
                </div>
                <Text fw={600} size="xl" c="gray.8">
                  PinkDesign
                </Text>
              </Group>

              <Title
                ta="center"
                className={css({
                  fontSize: "24px",
                  fontWeight: 700,
                  marginBottom: "8px",
                  color: "gray.900",
                  "@media (max-width: 768px)": {
                    fontSize: "20px",
                  },
                })}
              >
                Welcome back <i>!</i>
              </Title>

              <Text
                ta="center"
                c="gray.6"
                size="sm"
                mb="xl"
                className={css({
                  lineHeight: 1.5,
                  maxWidth: "320px",
                  margin: "0 auto 32px",
                })}
              ></Text>

              <Stack gap="md">
                <form onSubmit={handleSubmit(onSubmit)}>
                  <Stack gap="md">
                    {apiError && (
                      <Alert
                        icon={<MdError />}
                        color="red"
                        variant="light"
                        className={css({
                          borderRadius: "8px",
                        })}
                      >
                        {apiError}
                      </Alert>
                    )}

                    <TextInput
                      leftSection={<MdEmail color="#9ca3af" />}
                      placeholder="<EMAIL>"
                      size="md"
                      error={errors.email?.message}
                      className={css({
                        "& input": {
                          height: "48px",
                          border: "1px solid #e9ecef",
                          _focus: { borderColor: "#ff6b6b" },
                        },
                      })}
                      {...register("email", {
                        required: "Email is required",
                        pattern: {
                          value: /^\S+@\S+$/i,
                          message: "Invalid email address",
                        },
                      })}
                    />

                    <PasswordInput
                      leftSection={<MdLock color="#9ca3af" />}
                      placeholder="Enter your password"
                      size="md"
                      error={errors.password?.message}
                      className={css({
                        "& input": {
                          height: "48px",
                          border: "1px solid #e9ecef",
                          _focus: { borderColor: "#ff6b6b" },
                        },
                      })}
                      {...register("password", {
                        required: "Password is required",
                        minLength: {
                          value: 6,
                          message: "Password must be at least 6 characters",
                        },
                      })}
                    />

                    <Button
                      type="submit"
                      size="md"
                      loading={isSubmitting}
                      className={css({
                        background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                        border: "none",
                        color: "white",
                        height: "48px",
                        _hover: { opacity: 0.9 },
                      })}
                    >
                      Sign In
                    </Button>
                  </Stack>
                </form>

                <Text ta="center" c="gray.5" size="sm" my="md">
                  Or
                </Text>

                <Button
                  variant="default"
                  leftSection={<FcGoogle color="#4285f4" />}
                  onClick={handleGoogleLogin}
                  size="md"
                  className={css({
                    border: "1px solid #e9ecef",
                    backgroundColor: "white",
                    color: "gray.700",
                    _hover: { backgroundColor: "gray.50" },
                    height: "48px",
                  })}
                >
                  Continue with Google
                </Button>

                <Button
                  variant="default"
                  leftSection={<SiFacebook color="#00a1f1" />}
                  onClick={handleMicrosoftLogin}
                  size="md"
                  className={css({
                    border: "1px solid #e9ecef",
                    backgroundColor: "white",
                    color: "gray.700",
                    _hover: { backgroundColor: "gray.50" },
                    height: "48px",
                  })}
                >
                  Continue with Facebook
                </Button>

                <Text ta="center" c="gray.6" size="sm" mt="lg">
                  Do not have an account?{" "}
                  <Anchor
                    component={Link}
                    to="/account/signup"
                    className={css({
                      color: "#ff6b6b",
                      textDecoration: "none",
                    })}
                  >
                    Sign up
                  </Anchor>
                </Text>

                <Text ta="center" c="gray.5" size="xs" mt="xl">
                  By signing up, you agree to our{" "}
                  <Anchor href="#" c="gray.7" size="xs">
                    Terms of services
                  </Anchor>{" "}
                  &{" "}
                  <Anchor href="#" c="gray.7" size="xs">
                    Privacy policy
                  </Anchor>
                </Text>
              </Stack>
            </Box>
          </div>
        </Center>
      </Container>
    </div>
  );
}
