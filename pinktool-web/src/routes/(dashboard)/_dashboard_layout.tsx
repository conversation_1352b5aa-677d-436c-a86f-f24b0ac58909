import { useAccount } from "@/hooks/useAccount";
import useAuth<PERSON>heck from "@/hooks/useAuthCheck";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import { useEffect } from "react";

export const Route = createFileRoute("/(dashboard)/_dashboard_layout")({
  component: RouteComponent,
});

function RouteComponent() {
  const account = useAccount((state) => state.account);

  useAuthCheck();

  useEffect(() => {
    console.log("------account", account);

    if (account && account.status === "unverified") {
      console.log("unverified");
      window.location.href = "/account/verify";
    }
  }, [account]);

  if (!account) return <></>;

  return (
    <>
      <Outlet />
    </>
  );
}
