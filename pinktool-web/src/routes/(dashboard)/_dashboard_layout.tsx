import { useAccount } from "@/hooks/useAccount";
import useAuthCheck from "@/hooks/useAuthCheck";
import { createFileRoute, Outlet } from "@tanstack/react-router";

export const Route = createFileRoute("/(dashboard)/_dashboard_layout")({
  component: RouteComponent,
});

function RouteComponent() {
  const account = useAccount((state) => state.account);
  useAuthCheck();
  if (!account) return <></>;
  return (
    <>
      <Outlet />
    </>
  );
}
