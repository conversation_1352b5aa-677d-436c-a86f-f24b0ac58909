import { createFileRoute } from "@tanstack/react-router";
import {
  Container,
  Title,
  Text,
  Box,
  Table,
  Button,
  Badge,
  Group,
  Pagination,
  LoadingOverlay,
  Alert,
  Tabs,
  Menu,
  ActionIcon,
  Card,
  Stack,
} from "@mantine/core";
import { AppLayout } from "@/components/layout/AppLayout";
import { css } from "styled-system/css";
import { useAccount } from "@/hooks/useAccount";
import { AdminService } from "@/api/sdk.gen";
import type { AdminUpdateClientData } from "@/api/types.gen";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { MdError, MdVerified, MdCancel, MdMoreVert } from "react-icons/md";
import { TbUsers, TbChartBar, TbPalette, TbUserCog } from "react-icons/tb";

export const Route = createFileRoute("/(dashboard)/_dashboard_layout/admin")({
  component: RouteComponent,
});

function RouteComponent() {
  const account = useAccount((state) => state.account);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState<string | null>("insights");
  const queryClient = useQueryClient();
  console.log("admin");

  // Fetch clients - always call hooks, but conditionally enable the query
  const {
    data: clientsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["admin-clients", currentPage] as const,
    queryFn: ({ queryKey }) =>
      AdminService.getClients({
        query: { page: queryKey[1], limit: 10 },
      }),
    enabled: account?.role === "admin", // Only fetch if user is admin
  });

  // Update client status mutation - always call the hook
  const updateClientMutation = useMutation({
    mutationFn: ({ clientId, status }: { clientId: string; status: string }) =>
      AdminService.adminUpdateClient({
        path: { client_id: clientId },
        body: { status },
      } as AdminUpdateClientData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin-clients"] });
    },
  });

  // Early returns after all hooks are called
  if (account?.role !== "admin") {
    return (
      <AppLayout>
        <Container size="md" mt="xl">
          <Alert icon={<MdError />} title="Access Denied" color="red">
            You don't have permission to access this page.
          </Alert>
        </Container>
      </AppLayout>
    );
  }

  const handleStatusToggle = (clientId: string, currentStatus: string) => {
    const newStatus = currentStatus === "verified" ? "unverified" : "verified";
    updateClientMutation.mutate({ clientId, status: newStatus });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return (
          <Badge color="green" leftSection={<MdVerified size={12} />}>
            Verified
          </Badge>
        );
      case "unverified":
        return (
          <Badge color="red" leftSection={<MdCancel size={12} />}>
            Unverified
          </Badge>
        );
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      {/* Header with Tabs */}
      <Box
        className={css({
          backgroundColor: "white",
          borderBottom: "1px solid #E5E7EB",
        })}
      >
        <Container size="xl">
          <Box py="lg">
            <Title order={2} size="h2" mb={4} c="#1F2937">
              Admin Panel
            </Title>
            <Text size="sm" c="#6B7280" mb="md">
              Manage your platform with comprehensive admin tools.
            </Text>
          </Box>

          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="insights" leftSection={<TbChartBar size={16} />}>
                Insights
              </Tabs.Tab>
              <Tabs.Tab value="clients" leftSection={<TbUsers size={16} />}>
                Clients
              </Tabs.Tab>
              <Tabs.Tab value="designers" leftSection={<TbPalette size={16} />}>
                Designers
              </Tabs.Tab>
              <Tabs.Tab value="managers" leftSection={<TbUserCog size={16} />}>
                Managers
              </Tabs.Tab>
            </Tabs.List>
        </Container>
      </Box>

      {/* Content */}
      <Box
        className={css({
          flex: 1,
          padding: "16px",
        })}
      >
        <Container size="xl">
          <Tabs value={activeTab} onChange={setActiveTab}>

            <Tabs.Panel value="insights" pt="sm">
              <Card shadow="sm" padding="md" radius="md" withBorder>
                <Stack gap="sm">
                  <Title order={3}>Platform Insights</Title>
                  <Text c="dimmed">
                    Analytics and insights will be displayed here.
                  </Text>
                </Stack>
              </Card>
            </Tabs.Panel>

            <Tabs.Panel value="clients" pt="sm">
              {error && (
                <Alert icon={<MdError />} title="Error" color="red" mb="md">
                  Failed to load clients. Please try again.
                </Alert>
              )}

              <Card shadow="sm" padding="md" radius="md" withBorder>
                <Box pos="relative">
                  <LoadingOverlay visible={isLoading} />

                  {clientsData?.data && (
                    <>
                      <Table
                        className={css({
                          "& thead tr th": {
                            backgroundColor: "#F9FAFB",
                            color: "#374151",
                            fontWeight: "600",
                            fontSize: "14px",
                            padding: "12px",
                            borderBottom: "1px solid #E5E7EB",
                          },
                          "& tbody tr": {
                            borderBottom: "1px solid #F3F4F6",
                            "&:hover": {
                              backgroundColor: "#F9FAFB",
                            },
                          },
                          "& tbody tr td": {
                            padding: "12px",
                            fontSize: "14px",
                            color: "#374151",
                          },
                        })}
                      >
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>Name</Table.Th>
                            <Table.Th>Email</Table.Th>
                            <Table.Th>Status</Table.Th>
                            <Table.Th>Created At</Table.Th>
                            <Table.Th width={50}>Actions</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {clientsData.data.entries.map((client) => (
                            <Table.Tr key={client.id}>
                              <Table.Td>
                                <Text fw={500}>{client.full_name}</Text>
                              </Table.Td>
                              <Table.Td>
                                <Text c="dimmed">{client.email}</Text>
                              </Table.Td>
                              <Table.Td>{getStatusBadge(client.status)}</Table.Td>
                              <Table.Td>
                                <Text c="dimmed">
                                  {new Date(client.created_at).toLocaleDateString()}
                                </Text>
                              </Table.Td>
                              <Table.Td>
                                <Menu shadow="md" width={200}>
                                  <Menu.Target>
                                    <ActionIcon
                                      variant="subtle"
                                      color="gray"
                                      size="sm"
                                      loading={updateClientMutation.isPending}
                                    >
                                      <MdMoreVert size={16} />
                                    </ActionIcon>
                                  </Menu.Target>

                                  <Menu.Dropdown>
                                    <Menu.Item
                                      leftSection={
                                        client.status === "verified" ? (
                                          <MdCancel size={14} />
                                        ) : (
                                          <MdVerified size={14} />
                                        )
                                      }
                                      color={client.status === "verified" ? "red" : "green"}
                                      onClick={() => handleStatusToggle(client.id, client.status)}
                                    >
                                      {client.status === "verified" ? "Unverify" : "Verify"}
                                    </Menu.Item>
                                  </Menu.Dropdown>
                                </Menu>
                              </Table.Td>
                            </Table.Tr>
                          ))}
                        </Table.Tbody>
                      </Table>

                      {clientsData.data.total_entries > 10 && (
                        <Group justify="center" mt="md">
                          <Pagination
                            value={currentPage}
                            onChange={setCurrentPage}
                            total={Math.ceil(clientsData.data.total_entries / 10)}
                          />
                        </Group>
                      )}
                    </>
                  )}
                </Box>
              </Card>
            </Tabs.Panel>

            <Tabs.Panel value="designers" pt="sm">
              <Card shadow="sm" padding="md" radius="md" withBorder>
                <Stack gap="sm">
                  <Title order={3}>Designer Management</Title>
                  <Text c="dimmed">
                    Designer management features will be available here.
                  </Text>
                </Stack>
              </Card>
            </Tabs.Panel>

            <Tabs.Panel value="managers" pt="sm">
              <Card shadow="sm" padding="md" radius="md" withBorder>
                <Stack gap="sm">
                  <Title order={3}>Manager Management</Title>
                  <Text c="dimmed">
                    Manager management features will be available here.
                  </Text>
                </Stack>
              </Card>
            </Tabs.Panel>
          </Tabs>
        </Container>
      </Box>
    </AppLayout>
  );
}
