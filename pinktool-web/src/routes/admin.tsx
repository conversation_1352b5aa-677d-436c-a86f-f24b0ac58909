import { createFileRoute } from "@tanstack/react-router";
import { 
  Container, 
  Title, 
  Text, 
  Box, 
  Table, 
  Button, 
  Badge, 
  Group,
  Pagination,
  LoadingOverlay,
  Alert
} from "@mantine/core";
import { AppLayout } from "@/components/layout/AppLayout";
import { css } from "styled-system/css";
import { useAccount } from "@/hooks/useAccount";
import useAuthCheck from "@/hooks/useAuthCheck";
import { AdminService } from "@/api/sdk.gen";
import type { AdminUpdateClientData } from "@/api/types.gen";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { MdError, MdVerified, MdCancel } from "react-icons/md";

export const Route = createFileRoute("/admin")({
  component: RouteComponent,
});

function RouteComponent() {
  const account = useAccount((state) => state.account);
  const [currentPage, setCurrentPage] = useState(1);
  const queryClient = useQueryClient();
  
  useAuthCheck();
  
  // Redirect if not admin
  if (!account) return <></>;
  if (account.role !== 'admin') {
    return (
      <AppLayout>
        <Container size="md" mt="xl">
          <Alert icon={<MdError />} title="Access Denied" color="red">
            You don't have permission to access this page.
          </Alert>
        </Container>
      </AppLayout>
    );
  }

  // Fetch clients
  const { data: clientsData, isLoading, error } = useQuery({
    queryKey: ['admin-clients', currentPage],
    queryFn: () => AdminService.getClients({
      query: {
        page: currentPage,
        limit: 10
      }
    }),
  });

  // Update client status mutation
  const updateClientMutation = useMutation({
    mutationFn: ({ clientId, status }: { clientId: string; status: string }) =>
      AdminService.adminUpdateClient({
        path: { client_id: clientId },
        body: { status }
      } as AdminUpdateClientData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-clients'] });
    },
  });

  const handleStatusToggle = (clientId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'verified' ? 'unverified' : 'verified';
    updateClientMutation.mutate({ clientId, status: newStatus });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge color="green" leftSection={<MdVerified size={12} />}>Verified</Badge>;
      case 'unverified':
        return <Badge color="red" leftSection={<MdCancel size={12} />}>Unverified</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      {/* Header */}
      <Box
        className={css({
          backgroundColor: "white",
          borderBottom: "1px solid #E5E7EB",
          padding: "20px 24px",
        })}
      >
        <Title order={2} size="h2" mb={8} c="#1F2937">
          Admin Panel
        </Title>
        <Text size="sm" c="#6B7280">
          Manage clients and their verification status.
        </Text>
      </Box>

      {/* Content */}
      <Box
        className={css({
          flex: 1,
          padding: "24px",
        })}
      >
        <Container size="xl">
          {error && (
            <Alert icon={<MdError />} title="Error" color="red" mb="md">
              Failed to load clients. Please try again.
            </Alert>
          )}

          <Box pos="relative">
            <LoadingOverlay visible={isLoading} />
            
            {clientsData?.data && (
              <>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Email</Table.Th>
                      <Table.Th>Status</Table.Th>
                      <Table.Th>Created At</Table.Th>
                      <Table.Th>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {clientsData.data.entries.map((client) => (
                      <Table.Tr key={client.id}>
                        <Table.Td>{client.full_name}</Table.Td>
                        <Table.Td>{client.email}</Table.Td>
                        <Table.Td>{getStatusBadge(client.status)}</Table.Td>
                        <Table.Td>{new Date(client.created_at).toLocaleDateString()}</Table.Td>
                        <Table.Td>
                          <Button
                            size="xs"
                            variant={client.status === 'verified' ? 'outline' : 'filled'}
                            color={client.status === 'verified' ? 'red' : 'green'}
                            loading={updateClientMutation.isPending}
                            onClick={() => handleStatusToggle(client.id, client.status)}
                          >
                            {client.status === 'verified' ? 'Unverify' : 'Verify'}
                          </Button>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>

                {clientsData.data.total_entries > 10 && (
                  <Group justify="center" mt="md">
                    <Pagination
                      value={currentPage}
                      onChange={setCurrentPage}
                      total={Math.ceil(clientsData.data.total_entries / 10)}
                    />
                  </Group>
                )}
              </>
            )}
          </Box>
        </Container>
      </Box>
    </AppLayout>
  );
}
