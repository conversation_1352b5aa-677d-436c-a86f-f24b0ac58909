// Google OAuth Service
declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: GoogleInitConfig) => void;
          prompt: () => void;
          renderButton: (element: HTMLElement, config: GoogleButtonConfig) => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

interface GoogleInitConfig {
  client_id: string;
  callback: (response: GoogleCredentialResponse) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleButtonConfig {
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  size?: 'large' | 'medium' | 'small';
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  logo_alignment?: 'left' | 'center';
  width?: string;
  locale?: string;
}

interface GoogleCredentialResponse {
  credential: string;
  select_by?: string;
}

interface GoogleUserInfo {
  sub: string;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  email: string;
  email_verified: boolean;
  locale?: string;
}

class GoogleAuthService {
  private clientId: string;
  private isInitialized = false;

  constructor() {
    this.clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
    if (!this.clientId) {
      console.error('Google Client ID not found in environment variables');
    }
  }

  // Initialize Google OAuth
  initialize(callback: (userInfo: GoogleUserInfo) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isInitialized) {
        resolve();
        return;
      }

      // Wait for Google script to load
      const checkGoogleLoaded = () => {
        if (window.google?.accounts?.id) {
          window.google.accounts.id.initialize({
            client_id: this.clientId,
            callback: (response: GoogleCredentialResponse) => {
              this.handleCredentialResponse(response, callback);
            },
            auto_select: false,
            cancel_on_tap_outside: false,
          });
          this.isInitialized = true;
          resolve();
        } else {
          setTimeout(checkGoogleLoaded, 100);
        }
      };

      checkGoogleLoaded();
    });
  }

  // Handle the credential response from Google
  private handleCredentialResponse(
    response: GoogleCredentialResponse,
    callback: (userInfo: GoogleUserInfo) => void
  ) {
    try {
      // Decode the JWT token to get user info
      const userInfo = this.parseJwt(response.credential);
      callback(userInfo);
    } catch (error) {
      console.error('Error parsing Google credential:', error);
    }
  }

  // Parse JWT token to extract user information
  private parseJwt(token: string): GoogleUserInfo {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  }

  // Render Google Sign-In button
  renderButton(
    element: HTMLElement,
    config: GoogleButtonConfig = {}
  ): Promise<void> {
    return new Promise((resolve) => {
      const defaultConfig: GoogleButtonConfig = {
        theme: 'outline',
        size: 'large',
        text: 'continue_with',
        shape: 'rectangular',
        logo_alignment: 'left',
        width: '100%',
        ...config,
      };

      if (window.google?.accounts?.id) {
        window.google.accounts.id.renderButton(element, defaultConfig);
        resolve();
      } else {
        // Wait for Google to load
        setTimeout(() => {
          if (window.google?.accounts?.id) {
            window.google.accounts.id.renderButton(element, defaultConfig);
            resolve();
          }
        }, 100);
      }
    });
  }

  // Trigger the Google One Tap prompt
  prompt() {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.prompt();
    }
  }

  // Disable auto-select
  disableAutoSelect() {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.disableAutoSelect();
    }
  }
}

export const googleAuthService = new GoogleAuthService();
export type { GoogleUserInfo, GoogleButtonConfig };
